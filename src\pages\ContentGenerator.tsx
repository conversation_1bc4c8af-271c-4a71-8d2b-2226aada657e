import { useState, useEffect } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { ArrowLeft, Wand2, Download, Copy, RefreshCw, Loader2, CheckCircle, AlertCircle, Settings, Eye, Clock, Users, Timer, ExternalLink, Trash2 } from 'lucide-react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTranslation } from 'react-i18next';
import { supabase } from '@/integrations/supabase/client';
import { renderMarkdown } from '@/utils/markdown';

// 平台配置
const platforms = [
  { id: 'linkedin', name: 'LinkedIn', color: 'bg-blue-600', maxLength: 3000 },
  { id: 'twitter', name: 'Twitter', color: 'bg-sky-500', maxLength: 280 },
  { id: 'xiaohongshu', name: 'Rednote', color: 'bg-pink-500', maxLength: 1000 },
  { id: 'reddit', name: 'Reddit', color: 'bg-orange-500', maxLength: 10000 },
  { id: 'wechat', name: 'Wechat', color: 'bg-green-600', maxLength: 2000 }
];

const styles = [
  { id: 'engaging', name: '引人入胜', description: '生动有趣，吸引读者注意' },
  { id: 'professional', name: '专业严谨', description: '正式商务，逻辑清晰' },
  { id: 'casual', name: '轻松随意', description: '亲切自然，易于理解' },
  { id: 'creative', name: '创意新颖', description: '独特视角，富有创意' },
  { id: 'analytical', name: '分析深入', description: '数据驱动，深度分析' }
];

interface Post {
  id: string;
  title: string;
  content?: string;
  author?: string;
  url: string;
  platform?: string;
  source_name?: string;
  summary?: string;
  published_at?: string;
}

interface GeneratedContent {
  id?: string; // Database ID for deletion
  platform: string;
  content: string;
  hashtags?: string[];
  character_count: number;
  estimated_engagement?: string;
  platform_specific_notes?: string;
}

// 摘要数据接口
interface SummaryData {
  id: string;
  content: string;
  source_urls: string[];
  platform: string;
  source_name: string;
  created_at: string;
  metadata?: any;
}

const ContentGenerator = () => {
  const [searchParams] = useSearchParams();
  const summaryId = searchParams.get('summary_id');

  // 新的状态管理 - 支持摘要模式
  const [mode, setMode] = useState<'post' | 'summary'>(summaryId ? 'summary' : 'post');
  const [posts, setPosts] = useState<Post[]>([]);
  const [selectedPost, setSelectedPost] = useState<Post | null>(null);
  const [summaryData, setSummaryData] = useState<SummaryData | null>(null);
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>(['linkedin', 'twitter']);
  const [selectedStyle, setSelectedStyle] = useState('engaging');
  const [userInput, setUserInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [generatedContent, setGeneratedContent] = useState<GeneratedContent[]>([]);
  const [activeTab, setActiveTab] = useState(summaryId ? 'configure' : 'select');

  // 队列相关状态
  const [taskId, setTaskId] = useState<string | null>(null);
  const [taskStatus, setTaskStatus] = useState<'pending' | 'processing' | 'completed' | 'failed' | null>(null);
  const [queuePosition, setQueuePosition] = useState<number | null>(null);
  const [progress, setProgress] = useState<number>(0);
  const [estimatedWaitTime, setEstimatedWaitTime] = useState<number | null>(null);
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);

  const { toast } = useToast();
  const { user } = useAuth();
  const { language } = useLanguage();
  const { t } = useTranslation();

  // Helper function to get title from summary metadata
  const getTitleFromMetadata = (metadata: any, url: string, index: number = 0): string => {
    // Try different title fields based on the data structure
    if (metadata?.post_titles && Array.isArray(metadata.post_titles) && metadata.post_titles[index]) {
      return metadata.post_titles[index];
    }
    if (metadata?.post_title) {
      return metadata.post_title;
    }
    if (metadata?.episode_title) {
      return metadata.episode_title;
    }
    if (metadata?.video_title) {
      return metadata.video_title;
    }
    // Fallback to URL
    return url;
  };

  useEffect(() => {
    if (mode === 'summary' && summaryId) {
      console.log('Fetching summary data for ID:', summaryId);
      fetchSummaryData(summaryId);
    } else if (mode === 'post') {
      fetchPosts();
    }
  }, [mode, summaryId]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      clearPolling();
    };
  }, []);

  // 清理轮询
  useEffect(() => {
    return () => {
      clearPolling();
    };
  }, []);

  // 获取摘要数据
  const fetchSummaryData = async (id: string) => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('summaries')
        .select(`
          id,
          content,
          language,
          source_urls,
          metadata,
          created_at,
          posts!inner(
            datasources!inner(
              platform,
              source_name
            )
          )
        `)
        .eq('id', id)
        .single();

      if (error) throw error;

      if (data) {
        const summary: SummaryData = {
          id: data.id,
          content: data.content,
          source_urls: data.source_urls || [],
          platform: (data.posts as any)?.datasources?.platform || 'unknown',
          source_name: (data.posts as any)?.datasources?.source_name || 'unknown',
          created_at: data.created_at,
          metadata: data.metadata
        };
        setSummaryData(summary);
        setActiveTab('configure');
      }
    } catch (error: any) {
      console.error('Error fetching summary:', error);
      toast({
        title: t('contentGenerator.fetchSummaryError'),
        description: error.message || t('contentGenerator.fetchSummaryErrorDesc'),
        variant: 'destructive',
      });
      // 如果获取摘要失败，切换到post模式
      setMode('post');
      setActiveTab('select');
    } finally {
      setLoading(false);
    }
  };

  const fetchPosts = async () => {
    try {
      // Since posts table is cleared regularly, we get data from summaries instead
      const { data, error } = await supabase
        .from('summaries')
        .select('id, content, summary_type, language, created_at, source_urls, metadata')
        .order('created_at', { ascending: false })
        .limit(25);

      if (error) throw error;

      // Convert summaries to post-like format for compatibility
      const formattedPosts: Post[] = (data || []).map((summary: any) => {
        // Extract metadata from summary
        const metadata = summary.metadata || {};
        const sourceUrls = summary.source_urls || [];

        return {
          id: summary.id, // Use summary ID as post ID
          title: metadata.post_titles?.[0] || metadata.title || 'No Title',
          content: summary.content,
          author: metadata.author || 'Unknown',
          url: sourceUrls[0] || '#',
          platform: metadata.platform || 'Unknown',
          source_name: metadata.source_name || 'Unknown',
          summary: summary.content, // The summary content itself
          published_at: metadata.published_at || summary.created_at,
        };
      });

      setPosts(formattedPosts);
    } catch (error) {
      console.error('Error fetching posts:', error);
      toast({
        title: '加载失败',
        description: '无法加载内容列表',
        variant: 'destructive',
      });
    }
  };

  const handlePlatformToggle = (platformId: string) => {
    setSelectedPlatforms(prev => 
      prev.includes(platformId) 
        ? prev.filter(p => p !== platformId)
        : [...prev, platformId]
    );
  };

  // 队列状态轮询函数
  const pollTaskStatus = async (taskId: string) => {
    try {
      const { data, error } = await supabase.functions.invoke('content-generation-status', {
        body: { task_id: taskId }
      });

      if (error) throw error;

      if (data.success && data.data) {
        const task = data.data;
        setTaskStatus(task.status);
        setProgress(task.progress || 0);
        setQueuePosition(task.queue_position);

        if (task.status === 'completed') {
          // 任务完成，获取生成的内容
          if (task.result_ids && task.result_ids.length > 0) {
            await fetchGeneratedContent(task.result_ids);
          }
          setLoading(false);
          clearPolling();
          toast({
            title: '生成完成',
            description: `已成功生成 ${task.target_platforms.length} 个平台的内容`,
          });
        } else if (task.status === 'failed') {
          setLoading(false);
          clearPolling();
          toast({
            title: '生成失败',
            description: task.error_message || '内容生成失败',
            variant: 'destructive',
          });
        }
      }
    } catch (error: any) {
      console.error('Error polling task status:', error);
    }
  };

  // 清理轮询
  const clearPolling = () => {
    if (pollingInterval) {
      clearInterval(pollingInterval);
      setPollingInterval(null);
    }
  };

  // 获取生成的内容
  const fetchGeneratedContent = async (resultIds: string[]) => {
    try {
      const { data, error } = await supabase
        .from('user_generated_content')
        .select('id, target_platform, content, created_at, style, metadata')
        .in('id', resultIds)
        .order('created_at', { ascending: false });

      if (error) throw error;

      if (data) {
        const formattedContent = data.map(item => ({
          id: item.id, // Preserve database ID for deletion
          platform: item.target_platform,
          content: item.content,
          character_count: item.content.length,
          hashtags: [],
          estimated_engagement: 'Medium',
          platform_specific_notes: `Generated with ${item.style} style`
        }));
        setGeneratedContent(formattedContent);
      }
    } catch (error: any) {
      console.error('Error fetching generated content:', error);
    }
  };

  const generateContent = async () => {
    // 检查模式和必需参数
    if (mode === 'summary') {
      if (!summaryData || selectedPlatforms.length === 0) {
        toast({
          title: '请完善配置',
          description: '需要选择至少一个目标平台',
          variant: 'destructive',
        });
        return;
      }
    } else {
      if (!selectedPost || selectedPlatforms.length === 0) {
        toast({
          title: '请选择内容和平台',
          description: '需要选择一个内容源和至少一个目标平台',
          variant: 'destructive',
        });
        return;
      }
    }

    setLoading(true);
    setActiveTab('generate');
    setTaskStatus(null);
    setProgress(0);
    setQueuePosition(null);

    try {
      if (mode === 'summary') {
        // 使用队列系统
        const { data, error } = await supabase.functions.invoke('content-generation-queue', {
          body: {
            summary_id: summaryData!.id,
            target_platforms: selectedPlatforms,
            style: selectedStyle,
            user_input: userInput.trim() || undefined,
            user_id: user?.id,
            user_language: language
          }
        });

        if (error) throw error;

        if (data.success) {
          setTaskId(data.task_id);
          setQueuePosition(data.queue_position);
          setEstimatedWaitTime(data.estimated_wait_time);
          setTaskStatus('pending');

          toast({
            title: '任务已提交',
            description: `排队位置: ${data.queue_position}, 预计等待: ${data.estimated_wait_time}分钟`,
          });

          // 开始轮询任务状态
          const interval = setInterval(() => {
            pollTaskStatus(data.task_id);
          }, 2000); // 每2秒轮询一次
          setPollingInterval(interval);
        } else {
          throw new Error(data.error || '任务提交失败');
        }
      } else {
        // 原有的post模式逻辑（保持不变）
        const { data, error } = await supabase.functions.invoke('platform-content-generator', {
          body: {
            post_id: selectedPost!.id,
            target_platforms: selectedPlatforms,
            tone: selectedStyle,
            include_hashtags: true
          }
        });

        if (error) throw error;

        if (data.success) {
          setGeneratedContent(data.data.generated_content);
          toast({
            title: '生成成功',
            description: `已为 ${data.data.generation_stats.successful_generations} 个平台生成内容`,
          });
        } else {
          throw new Error(data.error || '内容生成失败');
        }
      }
    } catch (error: any) {
      console.error('Error generating content:', error);
      toast({
        title: '生成失败',
        description: error.message || '无法生成内容',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      toast({
        title: '已复制',
        description: '内容已复制到剪贴板',
      });
    } catch (error) {
      toast({
        title: '复制失败',
        description: '无法复制到剪贴板',
        variant: 'destructive',
      });
    }
  };

  const deleteContent = async (contentId: string) => {
    if (!contentId) {
      toast({
        title: '删除失败',
        description: '无法识别要删除的内容',
        variant: 'destructive',
      });
      return;
    }

    // Show confirmation dialog
    if (!confirm('确定要删除这个生成的内容吗？此操作不可撤销。')) {
      return;
    }

    try {
      // Delete from user_generated_content table
      const { error: deleteError } = await supabase
        .from('user_generated_content')
        .delete()
        .eq('id', contentId);

      if (deleteError) throw deleteError;

      // Find and update the corresponding task
      const { data: tasks, error: taskFetchError } = await supabase
        .from('content_generation_queue')
        .select('id, result_ids')
        .contains('result_ids', [contentId]);

      if (taskFetchError) {
        console.error('Error fetching tasks:', taskFetchError);
      } else if (tasks && tasks.length > 0) {
        // Process each task that contains this content ID
        for (const task of tasks) {
          const currentResultIds = task.result_ids || [];
          const updatedResultIds = currentResultIds.filter((id: string) => id !== contentId);

          if (updatedResultIds.length === 0) {
            // If no more content, delete the entire task
            const { error: taskDeleteError } = await supabase
              .from('content_generation_queue')
              .delete()
              .eq('id', task.id);

            if (taskDeleteError) {
              console.error('Error deleting task:', taskDeleteError);
            }
          } else {
            // Update the task with remaining result_ids
            const { error: taskUpdateError } = await supabase
              .from('content_generation_queue')
              .update({ result_ids: updatedResultIds })
              .eq('id', task.id);

            if (taskUpdateError) {
              console.error('Error updating task result_ids:', taskUpdateError);
            }
          }
        }
      }

      toast({
        title: '删除成功',
        description: '内容已成功删除',
      });

      // Update local state to remove the deleted content
      setGeneratedContent(prevContent =>
        prevContent.filter(content => content.id !== contentId)
      );

    } catch (error: any) {
      console.error('Error deleting content:', error);
      toast({
        title: '删除失败',
        description: error.message || '无法删除内容',
        variant: 'destructive',
      });
    }
  };

  const exportContent = () => {
    if (generatedContent.length === 0) return;

    const exportData = {
      source: {
        title: selectedPost?.title,
        platform: selectedPost?.platform,
        author: selectedPost?.author,
        url: selectedPost?.url
      },
      settings: {
        tone: selectedStyle,
        include_hashtags: true,
        platforms: selectedPlatforms
      },
      generated_content: generatedContent,
      generated_at: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `content-generation-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: '导出成功',
      description: '内容已导出为JSON文件',
    });
  };

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      <div className="container-wide mx-auto responsive-padding py-8 lg:py-12 space-4xl">
        {/* Header - Enhanced with better spacing and visual hierarchy */}
        <div className="mb-12 lg:mb-16 space-3xl">
          <Link
            to="/"
            className="inline-flex items-center text-muted-foreground hover:text-foreground mb-6 transition-all duration-300 hover:scale-105 magnetic"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回首页
          </Link>

          <div className="flex items-center justify-between">
            <div className="space-lg">
              <h1 className="text-heading-1 text-responsive-2xl font-bold text-gradient blur-fade-in">多平台内容生成</h1>
              <p className="text-body-large text-responsive-lg text-muted-foreground leading-relaxed slide-in-bottom">
                基于现有内容，为不同社交媒体平台生成适配的内容
              </p>
            </div>
            
            {generatedContent.length > 0 && (
              <Button
                onClick={exportContent}
                variant="outline"
                className="btn-outline modern-button shadow-card hover:shadow-glow bounce-in"
              >
                <Download className="h-4 w-4 mr-2" />
                导出内容
              </Button>
            )}
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-8 lg:space-y-12">
          <TabsList className="grid w-full grid-cols-3 glass-effect border-border/50 shadow-card hover:shadow-glow transition-all duration-300 scale-in">
            <TabsTrigger
              value="select"
              disabled={mode === 'summary'}
              className="hover:bg-primary/10 transition-all duration-300"
            >
              {mode === 'summary' ? t('contentGenerator.summaryPreview') : t('contentGenerator.selectContent')}
            </TabsTrigger>
            <TabsTrigger value="configure" className="hover:bg-primary/10 transition-all duration-300">配置参数</TabsTrigger>
            <TabsTrigger value="generate" className="hover:bg-primary/10 transition-all duration-300">生成内容</TabsTrigger>
          </TabsList>

          {/* 选择内容/摘要预览 - Enhanced with modern styling */}
          <TabsContent value="select" className="space-y-8">
            {mode === 'summary' ? (
              // 摘要模式：显示摘要预览
              <Card className="glass-effect border-border/50 shadow-card hover:shadow-glow transition-all duration-300 card-3d">
                <CardHeader className="space-md">
                  <CardTitle className="flex items-center gap-3 text-xl">
                    <div className="p-2 bg-gradient-primary rounded-lg shadow-card">
                      <FileText className="h-5 w-5 text-primary-foreground" />
                    </div>
                    {t('contentGenerator.summaryPreview')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {summaryData ? (
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{summaryData.platform}</Badge>
                        <Badge variant="secondary">{summaryData.source_name}</Badge>
                        <span className="text-sm text-muted-foreground">
                          {new Date(summaryData.created_at).toLocaleString()}
                        </span>
                      </div>

                      <div>
                        <Label className="text-sm font-medium">{t('contentGenerator.summary')}</Label>
                        <div className="mt-2 p-4 bg-muted/50 rounded-lg">
                          <p className="text-sm leading-relaxed whitespace-pre-line">
                            {summaryData.content}
                          </p>
                        </div>
                      </div>

                      {summaryData.source_urls && summaryData.source_urls.length > 0 && (
                        <div>
                          <Label className="text-sm font-medium">
                            原始链接 ({summaryData.source_urls.length} 个)
                          </Label>
                          <div className="mt-2 space-y-1 max-h-32 overflow-y-auto">
                            {summaryData.source_urls.map((url, index) => (
                              <div key={index} className="flex items-center gap-2 text-sm">
                                <ExternalLink className="h-3 w-3 text-muted-foreground" />
                                <a
                                  href={url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-600 hover:text-blue-800 underline truncate"
                                  title={getTitleFromMetadata(summaryData.metadata, url, index)}
                                >
                                  {getTitleFromMetadata(summaryData.metadata, url, index)}
                                </a>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      <div className="flex justify-end">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setActiveTab('configure')}
                        >
                          下一步：配置参数
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                      <p className="text-muted-foreground">{t('contentGenerator.loadingSummary')}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            ) : (
              // 原有的post选择模式
              <Card>
                <CardHeader>
                  <CardTitle>选择源内容</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <Select
                      value={selectedPost?.id || ''}
                      onValueChange={(value) => {
                        const post = posts.find(p => p.id === value);
                        setSelectedPost(post || null);
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择要转换的内容" />
                      </SelectTrigger>
                      <SelectContent>
                        {posts.map((post) => (
                          <SelectItem key={post.id} value={post.id}>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="text-xs">
                                {post.platform}
                              </Badge>
                              <span className="truncate max-w-md">{post.title}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    {selectedPost && (
                      <Card className="bg-muted/50">
                        <CardContent className="pt-6">
                          <div className="space-y-3">
                            <div>
                              <h4 className="font-medium">{selectedPost.title}</h4>
                              <div className="flex items-center gap-2 text-sm text-muted-foreground mt-1">
                                <Badge variant="outline">{selectedPost.platform}</Badge>
                                {selectedPost.author && <span>作者: {selectedPost.author}</span>}
                                {selectedPost.source_name && <span>来源: {selectedPost.source_name}</span>}
                              </div>
                            </div>

                            {selectedPost.summary && (
                              <div>
                                <Label className="text-sm font-medium">{t('contentGenerator.aiSummary')}</Label>
                                <p className="text-sm text-muted-foreground mt-1 line-clamp-3">
                                  {selectedPost.summary}
                                </p>
                              </div>
                            )}

                            <div className="flex justify-end">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setActiveTab('configure')}
                                disabled={!selectedPost}
                              >
                                下一步：配置参数
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* 配置参数 */}
          <TabsContent value="configure" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>目标平台</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {platforms.map((platform) => (
                      <div key={platform.id} className="flex items-center space-x-3">
                        <Checkbox
                          id={platform.id}
                          checked={selectedPlatforms.includes(platform.id)}
                          onCheckedChange={() => handlePlatformToggle(platform.id)}
                        />
                        <div className="flex items-center space-x-2 flex-1">
                          <div className={`w-3 h-3 rounded-full ${platform.color}`} />
                          <Label htmlFor={platform.id} className="flex-1">
                            {platform.name}
                          </Label>
                          <span className="text-xs text-muted-foreground">
                            {platform.maxLength} 字符
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>生成设置</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="style">内容风格</Label>
                    <Select value={selectedStyle} onValueChange={setSelectedStyle}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {styles.map((style) => (
                          <SelectItem key={style.id} value={style.id}>
                            <div>
                              <div className="font-medium">{style.name}</div>
                              <div className="text-xs text-muted-foreground">{style.description}</div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {mode === 'summary' && (
                    <div>
                      <Label htmlFor="userInput">您的想法和补充</Label>
                      <Textarea
                        id="userInput"
                        placeholder="请输入您对这个话题的想法、观点或希望强调的重点..."
                        value={userInput}
                        onChange={(e) => setUserInput(e.target.value)}
                        rows={4}
                        className="mt-1"
                      />
                      <div className="text-xs text-muted-foreground mt-1">
                        {userInput.length} 字符
                      </div>
                    </div>
                  )}

                  {mode === 'summary' && summaryData && (
                    <div className="p-3 bg-muted/50 rounded-lg text-sm">
                      <div className="font-medium mb-1">内容来源统计</div>
                      <div className="text-muted-foreground">
                        将基于 {summaryData.source_urls.length} 个链接的内容进行生成
                      </div>
                    </div>
                  )}

                  <div className="pt-4">
                    <Button
                      onClick={generateContent}
                      disabled={
                        (mode === 'summary' ? !summaryData : !selectedPost) ||
                        selectedPlatforms.length === 0 ||
                        loading
                      }
                      className="w-full"
                    >
                      {loading ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          生成中...
                        </>
                      ) : (
                        <>
                          <Wand2 className="h-4 w-4 mr-2" />
                          生成内容
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* 生成内容 */}
          <TabsContent value="generate" className="space-y-6">
            {loading ? (
              <div className="space-y-6">
                {/* 队列状态卡片 */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Clock className="h-5 w-5" />
                      内容生成队列
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* 任务状态 */}
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">任务状态:</span>
                      <div className="flex items-center gap-2">
                        {taskStatus === 'pending' && (
                          <>
                            <Timer className="h-4 w-4 text-orange-500" />
                            <Badge variant="secondary">排队中</Badge>
                          </>
                        )}
                        {taskStatus === 'processing' && (
                          <>
                            <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
                            <Badge variant="default">处理中</Badge>
                          </>
                        )}
                        {!taskStatus && (
                          <>
                            <Loader2 className="h-4 w-4 animate-spin text-primary" />
                            <Badge variant="outline">提交中</Badge>
                          </>
                        )}
                      </div>
                    </div>

                    {/* 队列位置 */}
                    {queuePosition && queuePosition > 0 && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">队列位置:</span>
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">第 {queuePosition} 位</span>
                        </div>
                      </div>
                    )}

                    {/* 预计等待时间 */}
                    {estimatedWaitTime && estimatedWaitTime > 0 && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">预计等待:</span>
                        <span className="text-sm text-muted-foreground">
                          约 {estimatedWaitTime} 分钟
                        </span>
                      </div>
                    )}

                    {/* 进度条 */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">进度:</span>
                        <span className="text-sm text-muted-foreground">{progress}%</span>
                      </div>
                      <Progress value={progress} className="w-full" />
                    </div>

                    {/* 目标平台 */}
                    <div className="space-y-2">
                      <span className="text-sm font-medium">目标平台:</span>
                      <div className="flex flex-wrap gap-2">
                        {selectedPlatforms.map(platformId => {
                          const platform = platforms.find(p => p.id === platformId);
                          return (
                            <Badge key={platformId} variant="outline" className="flex items-center gap-1">
                              <div className={`w-2 h-2 rounded-full ${platform?.color || 'bg-gray-500'}`} />
                              {platform?.name || platformId}
                            </Badge>
                          );
                        })}
                      </div>
                    </div>

                    {/* 取消按钮 */}
                    <div className="pt-2">
                      <Button
                        variant="outline"
                        onClick={() => {
                          setLoading(false);
                          clearPolling();
                          setActiveTab('configure');
                        }}
                        className="w-full"
                      >
                        取消生成
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : generatedContent.length > 0 ? (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">生成的内容</h3>
                  <div className="flex gap-2">
                    <Button variant="outline" onClick={() => setActiveTab('configure')}>
                      <Settings className="h-4 w-4 mr-2" />
                      重新配置
                    </Button>
                    <Button onClick={generateContent} disabled={loading}>
                      <RefreshCw className="h-4 w-4 mr-2" />
                      重新生成
                    </Button>
                  </div>
                </div>

                <div className="grid gap-6">
                  {generatedContent.map((content, index) => {
                    const platform = platforms.find(p => p.id === content.platform);
                    const isOverLimit = content.character_count > (platform?.maxLength || 0);
                    
                    return (
                      <Card key={index} className="relative">
                        <CardHeader>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <div className={`w-4 h-4 rounded-full ${platform?.color || 'bg-gray-500'}`} />
                              <CardTitle className="text-lg">{platform?.name || content.platform}</CardTitle>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Badge variant={isOverLimit ? 'destructive' : 'secondary'}>
                                {content.character_count} / {platform?.maxLength || 0}
                              </Badge>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => copyToClipboard(content.content)}
                              >
                                <Copy className="h-4 w-4" />
                              </Button>
                              {content.id && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => deleteContent(content.id!)}
                                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            <div
                              className="min-h-32 p-3 border rounded-md bg-muted/50 max-w-none text-sm leading-relaxed markdown-content"
                              dangerouslySetInnerHTML={{
                                __html: renderMarkdown(content.content)
                              }}
                            />
                            
                            {content.hashtags && content.hashtags.length > 0 && (
                              <div>
                                <Label className="text-sm font-medium">话题标签</Label>
                                <div className="flex flex-wrap gap-1 mt-1">
                                  {content.hashtags.map((hashtag, i) => (
                                    <Badge key={i} variant="outline" className="text-xs">
                                      {hashtag}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            )}
                            
                            {content.platform_specific_notes && (
                              <div>
                                <Label className="text-sm font-medium">平台建议</Label>
                                <p className="text-sm text-muted-foreground mt-1">
                                  {content.platform_specific_notes}
                                </p>
                              </div>
                            )}
                            
                            {isOverLimit && (
                              <div className="flex items-center space-x-2 text-destructive text-sm">
                                <AlertCircle className="h-4 w-4" />
                                <span>内容超出平台字符限制，建议编辑后使用</span>
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </div>
            ) : (
              <div className="text-center py-12">
                <Wand2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">准备生成内容</h3>
                <p className="text-muted-foreground">
                  请先选择内容源和配置参数，然后点击生成按钮
                </p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>

      <Footer />
    </div>
  );
};

export default ContentGenerator;
