{"nav": {"title": "FeedMe.Today", "subtitle": "Smart Content Aggregation", "login": "Login/Register", "logout": "Logout", "profile": "Profile", "admin": "Admin", "dailySummary": "Daily Summary", "socialContent": "Social Media Content", "language": "Language"}, "auth": {"signOutSuccess": "Signed out successfully", "signOutSuccessDesc": "You have been successfully signed out", "signOutError": "Sign out failed", "signOutErrorDesc": "Please try again later", "adminBadge": "Admin", "loginAccount": "<PERSON><PERSON> Account", "createAccount": "Create Account", "login": "<PERSON><PERSON>", "register": "Register", "email": "Email", "password": "Password", "emailPlaceholder": "Enter your email", "passwordPlaceholder": "Enter your password", "welcomeBack": "Welcome back! Please login to your account", "createNewAccount": "Create your new account", "loginButton": "<PERSON><PERSON>", "registerButton": "Register", "googleLogin": "Sign in with Google", "googleRegister": "Sign up with Google", "noAccount": "Don't have an account? Sign up", "hasAccount": "Already have an account? Sign in", "backToHome": "Back to Home", "loginSuccess": "Login successful", "loginSuccessDesc": "Welcome back!", "loginFailed": "<PERSON><PERSON> failed", "invalidCredentials": "Invalid email or password", "registerSuccess": "Registration successful", "registerSuccessDesc": "Please check your email to verify your account", "registerFailed": "Registration failed", "emailRequired": "Please enter a valid email address", "passwordRequired": "Password must be at least 6 characters", "processing": "Processing login...", "processingSuccess": "Login successful! Redirecting...", "processingError": "Login processing failed", "sessionNotFound": "Login session not found", "processingFailed": "An error occurred while processing login", "tryAgainLater": "Please try again later"}, "home": {"heroTitle": "Smart Content Aggregation Platform", "heroSubtitle": "Aggregate content from multiple platforms, generate intelligent summaries, and create social media content", "getStarted": "Get Started", "learnMore": "Learn More", "stats": {"topics": "Topic Categories", "content": "Aggregated Content", "platforms": "Content Platforms", "users": "Active Users"}, "features": {"title": "Platform Features", "smartAggregation": {"title": "Smart Aggregation", "desc": "Automatically aggregate content from multiple social platforms"}, "aiSummary": {"title": "AI Summary", "desc": "Generate intelligent summaries using advanced AI technology"}, "contentGeneration": {"title": "Content Generation", "desc": "Create social media content for different platforms"}}, "topics": {"title": "Topic Categories", "subtitle": "Explore content across different topics", "viewAll": "View All Content", "todaySummaries": "Today's Summaries", "todayContent": "Today's Generated Content", "noTopics": "No topics available"}, "cta": {"title": "Start Your Content Aggregation Journey", "subtitle": "Join us to experience the power of intelligent content aggregation, let AI help you filter and summarize the most valuable information"}, "loading": "Loading topics...", "error": "Failed to load topics", "retry": "Retry"}, "contentSummary": {"title": "Daily Summary", "filters": {"title": "Search and Filter", "topic": "Topic", "allTopics": "All Topics", "platform": "Platform", "allPlatforms": "All Platforms", "date": "Date", "dateRange": "Date Range", "timezone": "Time", "today": "Today", "search": "Search", "searchPlaceholder": "Search keywords...", "clearFilters": "Clear Filters", "custom": "Custom"}, "summary": {"generateContent": "Generate Social Media Post", "favorite": "Favorite", "unfavorite": "Unfavorite", "readMore": "Read More", "readLess": "Read Less", "sources": "Sources"}, "empty": {"title": "No summaries found", "description": "Try adjusting your filters or check back later", "noDataSourceSummaries": "This data source has no summaries"}, "loading": "Loading summaries...", "stats": {"summaries": "Summaries", "dataSources": "Data Sources", "content": "Content", "totalPages": "Total Pages"}, "dataSourceList": "Sources", "allDataSources": "All Data Sources", "viewAllSummaries": "View All Summaries", "allSummaries": "All Summaries", "dataSource": "Data Source", "summaries": "Summaries", "itemsCount": "items", "unknownTopic": "Unknown Topic", "unknownSource": "Unknown Source", "favorites": {"favorite": "Favorite", "unfavorite": "Unfavorite", "favorited": "Favorited", "favoriteDataSource": "Favorite Data Source", "unfavoriteDataSource": "Unfavorite Data Source", "favoriteSummary": "Favorite Summary", "unfavoriteSummary": "Unfavorite Summary", "showFavoriteDataSources": "Show favorite data sources only", "showFavoriteSummaries": "Show favorite summaries only", "success": {"favoriteAdded": "Favorited Successfully", "dataSourceAdded": "Data source has been added to favorites", "summaryAdded": "Summary has been added to favorites", "favoriteRemoved": "Unfavorited", "dataSourceRemoved": "Data source has been removed from favorites", "summaryRemoved": "Summary has been removed from favorites"}, "errors": {"loginRequired": "Please log in first", "loginRequiredDesc": "You need to log in to favorite items", "operationFailed": "Operation failed", "fetchFailed": "Failed to fetch favorites", "cannotLoadDataSources": "Unable to load favorite data sources", "cannotLoadSummaries": "Unable to load favorite summaries", "favoriteFailed": "Failed to favorite", "unfavoriteFailed": "Failed to unfavorite"}}}, "contentHistory": {"title": "Social Media Content", "filters": {"status": "Status", "allStatuses": "All Statuses", "targetPlatform": "Target Platform", "allPlatforms": "All Platforms", "style": "Style", "allStyles": "All Styles", "topic": "Topic", "allTopics": "All Topics", "dataSource": "Data Source", "allDataSources": "All Data Sources", "platform": "Platform", "search": "Search", "searchPlaceholder": "Search content, title, data source...", "clearFilters": "Clear Filters", "filterAndSearch": "Filter and Search"}, "content": {"delete": "Delete", "confirmDelete": "Are you sure you want to delete this content?", "deleteSuccess": "Content deleted successfully", "deleteError": "Failed to delete content"}, "status": {"pending": "Pending", "processing": "Processing", "completed": "Completed", "failed": "Failed", "cancelled": "Cancelled"}, "empty": {"title": "No task records", "description": "You haven't submitted any content generation tasks yet"}, "loading": "Loading content history...", "stats": {"totalTasks": "Total Tasks", "completedTasks": "Completed", "generatedContent": "Generated Content"}, "taskList": "Task List", "itemsCount": "items", "labels": {"generationTask": "Content Generation Task", "targetPlatform": "Target Platform", "generationStyle": "Generation Style", "userIdea": "User Idea", "dataSourceInfo": "Data Source Information", "articleTitle": "Article Title", "articleLink": "Article Link", "dataSource": "Data Source", "dataSourcePlatform": "Data Source Platform", "dataSourceTopic": "Data Source Topic", "dataSourcePublishTime": "Data Source Publish Time", "generationInfo": "Generation Information", "taskStatus": "Task Status", "queuePosition": "Queue Position", "completionTime": "Completion Time", "sourceLinksCount": "Contains {{count}} original links", "creationTime": "Creation Time", "startTime": "Start Time", "errorMessage": "Error Message", "retryCount": "Retried {{current}}/{{max}} times", "generatedContent": "Generated Content", "unknown": "Unknown"}, "actions": {"copy": "Copy", "delete": "Delete", "retry": "Retry", "copied": "<PERSON>pied", "copiedDesc": "Content copied to clipboard", "copyFailed": "Co<PERSON> failed", "copyFailedDesc": "Unable to copy to clipboard", "deleteConfirm": "Are you sure you want to delete this generated content? This action cannot be undone.", "deleteSuccess": "Delete successful", "deleteSuccessDesc": "Content successfully deleted", "deleteFailed": "Delete failed", "deleteFailedDesc": "Unable to delete content"}, "errors": {"userNotLoggedIn": "User not logged in", "loadFailed": "Load failed"}}, "platformFilter": {"selectedCount": "Selected {count} platforms"}, "platforms": {"blog": "Blog", "xiaohongshu": "Rednote", "wechat": "WeChat", "podcast": "Podcast"}, "styles": {"engaging": "Engaging", "professional": "Professional", "casual": "Casual", "creative": "Creative", "analytical": "Analytical"}, "topicDetail": {"searchPlaceholder": "Search title, content, author...", "filter": "Filter", "platformFilter": "Platform Filter", "timeFilter": "Time Filter", "totalContent": "{count} items in total", "filtered": "(filtered)", "noContent": "No content available", "adjustFilters": "Try adjusting filter conditions or search keywords", "clearAllFilters": "Clear All Filters", "loading": "Loading topic information...", "topicNotFound": "Topic not found", "noDescription": "No description available", "selectDataSource": "Select data source", "allDataSources": "All data sources", "selectDate": "Select date", "allDates": "All dates", "summaryContent": "Summary Content", "generatedContent": "Generated Content", "loadingSummaries": "Loading summaries...", "loadFailed": "Load failed", "summaryCount": "{{count}} summaries", "originalLink": "Original Link", "relatedArticles": "Related Articles ({{count}} articles)", "noSummaryData": "No summary data available", "loadingGeneratedContent": "Loading generated content...", "generatedContentCount": "{{count}} generated content", "noGeneratedContent": "No generated content available"}, "dateRangePicker": {"placeholder": "Select date range", "quickSelect": "Quick Select", "today": "Today", "yesterday": "Yesterday", "last7days": "Last 7 days", "last30days": "Last 30 days", "thisMonth": "This month", "lastMonth": "Last month", "selectMode": "Select Mode", "singleDate": "Single date", "dateRange": "Date range"}, "timezoneSelector": {"placeholder": "Select timezone"}, "pagination": {"showing": "Showing", "of": "of", "items": "items", "itemsPerPage": "Items per page", "previous": "Previous", "next": "Next"}, "contentGeneration": {"title": "Generate Multi-Platform Content", "description": "Generate tailored content for different social media platforms based on the selected summary", "summaryInfo": "Summary Information", "sourceLinks": "Contains {{count}} original links", "targetPlatforms": "Target Platforms", "contentStyle": "Content Style", "userInput": "Your Thoughts and Additions", "optional": "optional", "userInputPlaceholder": "Please enter your thoughts, viewpoints, or key points you'd like to emphasize...", "submitting": "Submitting...", "startGeneration": "Start Generation", "styles": {"engaging": "Engaging", "professional": "Professional", "casual": "Casual", "creative": "Creative", "analytical": "Analytical"}, "errors": {"selectPlatform": "Please select platforms", "selectPlatformDesc": "At least one target platform must be selected", "submitFailed": "Task submission failed", "failed": "Generation failed", "cannotGenerate": "Unable to generate content"}, "success": {"submitted": "Task submitted", "queueInfo": "Queue position: {{position}}, estimated wait: {{waitTime}} minutes"}, "premiumRequired": "Premium subscription required", "premiumRequiredDesc": "Free users cannot use content generation feature. Please upgrade to premium to unlock all features", "upgradeToPremium": "Upgrade to Premium"}, "sourceSubmission": {"title": "Submit Data Source", "description": "Suggest a new data source for our platform", "button": "Submit Source", "form": {"topic": "Topic", "topicPlaceholder": "Select a topic", "topicCustom": "Custom Topic", "topicCustomPlaceholder": "Enter custom topic name", "platform": "Platform", "platformPlaceholder": "Select a platform", "platformCustom": "Custom Platform", "platformCustomPlaceholder": "Enter custom platform name", "url": "URL", "urlPlaceholder": "Enter the source URL", "other": "Other"}, "validation": {"topicRequired": "Please select a topic", "topicCustomRequired": "Please enter a custom topic name", "platformRequired": "Please select a platform", "platformCustomRequired": "Please enter a custom platform name", "urlRequired": "Please enter a URL", "urlInvalid": "Please enter a valid URL", "topicTooLong": "Topic name is too long (max 100 characters)", "platformTooLong": "Platform name is too long (max 50 characters)", "urlTooLong": "URL is too long (max 500 characters)"}, "submitting": "Submitting...", "submit": "Submit", "cancel": "Cancel", "success": {"title": "Submitted Successfully", "message": "Thank you for your suggestion! We will review it within 1-2 days and add it to our platform if approved."}, "error": {"title": "Submission Failed", "authRequired": "Please log in to submit a data source", "duplicateUrl": "You have already submitted this URL and it is pending review", "networkError": "Network error, please try again", "serverError": "Server error, please try again later", "unknownError": "An unknown error occurred, please try again"}}, "contentGenerator": {"summaryPreview": "Summary Preview", "selectContent": "Select Content", "summary": "Summary", "aiSummary": "AI Summary", "loadingSummary": "Loading summary data...", "fetchSummaryError": "Failed to get summary", "fetchSummaryErrorDesc": "Unable to get summary data"}, "admin": {"title": "Admin Dashboard", "tabs": {"overview": "Overview", "topics": "Topics", "dataSources": "Data Sources", "users": "Users", "content": "Content"}, "status": {"disabled": "Disabled", "normal": "Normal", "pending": "Pending Crawl"}}, "emailSubscription": {"title": "Email Subscription", "description": "Subscribe to daily email summaries to get the latest content updates", "subscribe": "Subscribe", "subscribed": "Subscribed", "unsubscribe": "Unsubscribe", "basicSettings": "Basic Settings", "enableEmail": "Enable Email Subscription", "enableEmailDesc": "Receive daily email summaries at your preferred time", "emailLanguage": "Email Language", "timezone": "Timezone", "selectTimezone": "Select timezone", "sendTime": "Send Time", "selectSendTime": "Select send time", "sendTimeDesc": "Emails will be sent at the specified time in your selected timezone", "favoritesOnly": "Favorites Only", "favoritesOnlyDesc": "Only receive content from your favorite data sources", "podcastEnabled": "Include Podcast Summary", "podcastEnabledDesc": "Include AI-generated podcast based on your subscribed topics in emails", "contentFilters": "Content Filters", "topics": "Topics", "platforms": "Platforms", "selectAll": "Select All", "subscribeSuccess": "Email subscription updated successfully!", "unsubscribeSuccess": "Email subscription cancelled", "updateError": "Failed to update subscription settings, please try again", "premiumRequired": "Premium subscription required", "premiumRequiredDesc": "Free users cannot use email subscription feature. Please upgrade to premium to unlock all features", "upgradeToPremium": "Upgrade to Premium"}, "profile": {"title": "Profile", "settings": "Settings", "preferences": "Preferences", "backToHome": "Back to Home", "subtitle": "Manage your account information and preferences", "basicInfo": "Basic Information", "edit": "Edit", "email": "Email Address", "emailCannotChange": "Email address cannot be modified", "role": "User Role", "admin": "Admin", "user": "User", "displayName": "Display Name", "displayNamePlaceholder": "Enter your display name", "notSet": "Not set", "avatarUrl": "Avatar URL", "avatarUrlPlaceholder": "Enter avatar image URL", "cancel": "Cancel", "saving": "Saving...", "save": "Save", "saveSuccess": "Saved successfully", "saveSuccessDesc": "Your profile has been updated", "saveError": "Save failed", "saveErrorDesc": "Unable to update profile", "accountStats": "Account Statistics", "registrationDate": "Registration Date", "lastLogin": "Last Login", "never": "Never", "emailStatus": "Email Status", "verified": "Verified", "unverified": "Unverified", "permissions": "Permissions", "userPermissions": "Can access:", "userPermissionsList": ["Browse all topics and content", "Use content generation features", "View system dashboard", "Manage personal profile"], "adminPermissions": "Additional permissions:", "adminPermissionsList": ["Manage topics and data sources", "Manually trigger crawler tasks", "Batch operation features", "View all user profiles", "System configuration and monitoring"]}, "common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "edit": "Edit", "delete": "Delete", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "clear": "Clear", "refresh": "Refresh", "viewMore": "View More", "viewLess": "View Less", "more": "more", "selectAll": "Select All", "noResults": "No results found", "freeLimitReached": "Free user limit", "freeLimitDesc": "Free users can only view today's and yesterday's content. Please upgrade to premium to view more historical content", "upgradeToPremium": "Upgrade to Premium"}, "footer": {"description": "Aggregate quality content from multiple platforms, AI intelligent summaries help you quickly get core information and keep up with industry trends.", "product": {"title": "Product Features", "topicRecommendation": "Topic Recommendation", "platformAggregation": "Platform Aggregation", "aiSummary": "AI Summary", "contentDiscovery": "Content Discovery"}, "platform": {"title": "Supported Platforms", "xiaohongshu": "Rednote", "blog": "Blog", "podcast": "Podcast"}, "company": {"title": "Company Info", "aboutUs": "About Us", "contactUs": "Contact Us", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service"}, "copyright": "© 2024 FeedMe.Today. All rights reserved.", "poweredBy": "Powered By FeedMe.Today"}}