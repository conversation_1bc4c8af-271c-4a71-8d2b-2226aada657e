@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* 活力社交风配色 - 橙粉青主题 */
    --background: 30 100% 98%;
    --foreground: 20 15% 15%;

    --card: 30 100% 99%; /* 非常浅的暖色调，几乎透明 */
    --card-foreground: 20 15% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 20 15% 15%;

    --primary: 15 90% 55%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 15 100% 65%;

    --secondary: 30 100% 96%;
    --secondary-foreground: 20 15% 20%;

    --muted: 30 50% 95%;
    --muted-foreground: 20 10% 50%;

    --accent: 315 85% 90%;
    --accent-foreground: 315 85% 25%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    /* 语义化状态颜色 */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;
    --success-light: 142 76% 95%;

    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 98%;
    --warning-light: 38 92% 95%;

    --info: 199 89% 48%;
    --info-foreground: 0 0% 98%;
    --info-light: 199 89% 95%;

    --error: 0 84% 60%;
    --error-foreground: 0 0% 98%;
    --error-light: 0 84% 95%;

    --border: 30 50% 90%;
    --input: 30 50% 90%;
    --ring: 15 90% 55%;

    /* 字体系统 */
    --font-family-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-family-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
    --font-family-mono: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;

    /* 字体大小层级 */
    --text-xs: 0.75rem;     /* 12px */
    --text-sm: 0.875rem;    /* 14px */
    --text-base: 1rem;      /* 16px */
    --text-lg: 1.125rem;    /* 18px */
    --text-xl: 1.25rem;     /* 20px */
    --text-2xl: 1.5rem;     /* 24px */
    --text-3xl: 1.875rem;   /* 30px */
    --text-4xl: 2.25rem;    /* 36px */
    --text-5xl: 3rem;       /* 48px */
    --text-6xl: 3.75rem;    /* 60px */
    --text-7xl: 4.5rem;     /* 72px */

    /* 行高系统 */
    --leading-none: 1;
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;

    /* 字重系统 */
    --font-thin: 100;
    --font-extralight: 200;
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    --font-extrabold: 800;
    --font-black: 900;

    /* 8px基础间距系统 */
    --space-unit: 4px;
    --space-xs: 8px;
    --space-sm: 12px;
    --space-md: 16px;
    --space-lg: 24px;
    --space-xl: 32px;
    --space-2xl: 48px;
    --space-3xl: 64px;
    --space-4xl: 96px;

    /* Z-index层级管理 */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;

    /* 增强的渐变系统 */
    --gradient-primary: linear-gradient(135deg, hsl(15 90% 55%), hsl(315 85% 60%));
    --gradient-secondary: linear-gradient(135deg, hsl(195 100% 70%), hsl(315 85% 75%));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%), hsl(30 100% 98%));
    --gradient-vibrant: linear-gradient(135deg, hsl(15 90% 55%), hsl(195 100% 60%), hsl(315 85% 60%));
    --gradient-hero: linear-gradient(135deg, hsl(15 90% 55%), hsl(315 85% 60%), hsl(195 100% 70%));
    --gradient-card-hover: linear-gradient(145deg, hsl(0 0% 100%), hsl(15 100% 97%), hsl(315 100% 97%));
    --gradient-button-primary: linear-gradient(135deg, hsl(15 90% 55%), hsl(315 85% 60%));
    --gradient-text: linear-gradient(135deg, hsl(15 90% 45%), hsl(315 85% 50%));

    /* 多层次阴影系统 */
    --shadow-xs: 0 1px 2px 0 hsl(0 0% 0% / 0.05);
    --shadow-sm: 0 1px 3px 0 hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow-md: 0 4px 6px -1px hsl(0 0% 0% / 0.1), 0 2px 4px -2px hsl(0 0% 0% / 0.1);
    --shadow-lg: 0 10px 15px -3px hsl(0 0% 0% / 0.1), 0 4px 6px -4px hsl(0 0% 0% / 0.1);
    --shadow-xl: 0 20px 25px -5px hsl(0 0% 0% / 0.1), 0 8px 10px -6px hsl(0 0% 0% / 0.1);
    --shadow-2xl: 0 25px 50px -12px hsl(0 0% 0% / 0.25);
    --shadow-elegant: 0 4px 25px -4px hsl(15 90% 55% / 0.15);
    --shadow-card: 0 3px 15px -3px hsl(315 85% 60% / 0.1);
    --shadow-card-rest: 0 1px 3px hsl(15 90% 55% / 0.1), 0 1px 2px hsl(315 85% 60% / 0.06);
    --shadow-card-hover: 0 10px 25px hsl(15 90% 55% / 0.15), 0 4px 10px hsl(315 85% 60% / 0.1);
    --shadow-button: 0 4px 14px hsl(15 90% 55% / 0.25);
    --shadow-glow: 0 0 40px hsl(15 90% 55% / 0.25);
    --shadow-glow-intense: 0 0 50px hsl(15 90% 55% / 0.3), 0 0 100px hsl(315 85% 60% / 0.2);

    /* 动画缓动函数 */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-spring: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    --transition-bounce: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-elastic: all 0.8s cubic-bezier(0.68, -0.6, 0.32, 1.6);

    /* 磨砂玻璃效果 */
    --glass-effect: backdrop-blur(20px) saturate(180%);
    --glass-border: rgba(255, 255, 255, 0.125);

    /* 悬浮效果 */
    --hover-lift: 0 10px 40px -10px hsl(15 90% 55% / 0.3);
    --hover-glow: 0 0 20px hsl(315 85% 60% / 0.4);

    /* 脉冲动画 */
    --pulse-primary: 0 0 0 0 hsl(15 90% 55% / 0.7);
    --pulse-secondary: 0 0 0 0 hsl(315 85% 60% / 0.7);

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 6%; /* 稍微亮一点，但仍然很接近背景 */
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    /* 深色模式语义化状态颜色 */
    --success: 142 76% 45%;
    --success-foreground: 210 40% 98%;
    --success-light: 142 76% 15%;

    --warning: 38 92% 60%;
    --warning-foreground: 210 40% 98%;
    --warning-light: 38 92% 15%;

    --info: 199 89% 58%;
    --info-foreground: 210 40% 98%;
    --info-light: 199 89% 15%;

    --error: 0 84% 70%;
    --error-foreground: 210 40% 98%;
    --error-light: 0 84% 15%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* 增强的布局系统 */
  .container-fluid {
    @apply w-full max-w-none;
  }

  .container-sm { @apply max-w-2xl; }
  .container-md { @apply max-w-4xl; }
  .container-lg { @apply max-w-6xl; }
  .container-xl { @apply max-w-full; }
  .container-2xl { @apply max-w-full; }

  /* 大屏幕优化容器 - 纯CSS版本，确保居中对称 */
  .container-wide {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    max-width: min(95vw, 1400px);
  }

  @media (min-width: 1280px) {
    .container-wide {
      max-width: min(90vw, 1600px);
    }
  }

  @media (min-width: 1536px) {
    .container-wide {
      max-width: min(85vw, 1800px);
    }
  }

  .container-ultra-wide {
    @apply w-full mx-auto;
    max-width: min(98vw, 1600px);
  }

  @media (min-width: 1280px) {
    .container-ultra-wide {
      max-width: min(95vw, 1800px);
    }
  }

  @media (min-width: 1536px) {
    .container-ultra-wide {
      max-width: min(92vw, 2000px);
    }
  }

  /* 响应式网格系统 */
  .grid-responsive {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6 lg:gap-8;
  }

  .grid-adaptive {
    @apply grid gap-4;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  }

  /* 正方形卡片网格 */
  .grid-square-cards {
    @apply grid gap-4 md:gap-6 lg:gap-8;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }

  @media (min-width: 768px) {
    .grid-square-cards {
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    }
  }

  @media (min-width: 1024px) {
    .grid-square-cards {
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }
  }

  /* 字体工具类 */
  .text-display-1 {
    font-size: var(--text-7xl);
    line-height: var(--leading-none);
    font-weight: var(--font-bold);
    letter-spacing: -0.025em;
  }

  .text-display-2 {
    font-size: var(--text-6xl);
    line-height: var(--leading-tight);
    font-weight: var(--font-bold);
    letter-spacing: -0.025em;
  }

  .text-display-3 {
    font-size: var(--text-5xl);
    line-height: var(--leading-tight);
    font-weight: var(--font-semibold);
    letter-spacing: -0.025em;
  }

  .text-heading-1 {
    font-size: var(--text-4xl);
    line-height: var(--leading-tight);
    font-weight: var(--font-semibold);
  }

  .text-heading-2 {
    font-size: var(--text-3xl);
    line-height: var(--leading-snug);
    font-weight: var(--font-semibold);
  }

  .text-heading-3 {
    font-size: var(--text-2xl);
    line-height: var(--leading-snug);
    font-weight: var(--font-medium);
  }

  .text-body-large {
    font-size: var(--text-lg);
    line-height: var(--leading-relaxed);
    font-weight: var(--font-normal);
  }

  .text-body {
    font-size: var(--text-base);
    line-height: var(--leading-normal);
    font-weight: var(--font-normal);
  }

  .text-body-small {
    font-size: var(--text-sm);
    line-height: var(--leading-normal);
    font-weight: var(--font-normal);
  }

  .text-caption {
    font-size: var(--text-xs);
    line-height: var(--leading-normal);
    font-weight: var(--font-medium);
  }

  /* 间距工具类 */
  .space-unit { @apply space-y-1; }
  .space-xs { @apply space-y-2; }
  .space-sm { @apply space-y-3; }
  .space-md { @apply space-y-4; }
  .space-lg { @apply space-y-6; }
  .space-xl { @apply space-y-8; }
  .space-2xl { @apply space-y-12; }
  .space-3xl { @apply space-y-16; }
  .space-4xl { @apply space-y-24; }

  /* 标准化按钮样式 */
  .btn-primary {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium;
    @apply bg-gradient-to-r from-primary to-primary-glow text-primary-foreground;
    @apply shadow-lg;
    box-shadow: var(--shadow-button);
    @apply transition-all duration-300 ease-out transform-gpu;
    @apply hover:scale-105 hover:-translate-y-1;
    @apply active:scale-95;
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  .btn-primary:hover {
    box-shadow: var(--shadow-glow-intense);
  }

  .btn-secondary {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium;
    @apply bg-secondary text-secondary-foreground border border-border;
    @apply shadow-sm hover:shadow-card;
    @apply transition-all duration-300 ease-out;
    @apply hover:bg-secondary/80 hover:scale-105;
    @apply active:scale-95;
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  .btn-outline {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium;
    @apply bg-transparent text-foreground border border-border;
    @apply transition-all duration-300 ease-out;
    @apply hover:bg-primary/10 hover:border-primary/40 hover:scale-105;
    @apply active:scale-95;
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  .btn-ghost {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium;
    @apply bg-transparent text-foreground;
    @apply transition-all duration-300 ease-out;
    @apply hover:bg-accent hover:text-accent-foreground hover:scale-105;
    @apply active:scale-95;
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  /* 按钮尺寸变体 */
  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  .btn-xl {
    @apply px-8 py-4 text-lg;
  }

  /* 现代化交互效果 */
  .interactive-card {
    @apply transition-all duration-300 ease-out transform-gpu;
    @apply hover:scale-[1.02] hover:-translate-y-2;
    @apply hover:shadow-[var(--hover-lift)];
    @apply active:scale-[0.98];
  }

  .card-3d {
    @apply transition-all duration-300 ease-out transform-gpu;
    @apply hover:scale-[1.03] hover:-translate-y-3 hover:rotate-1;
    box-shadow: var(--shadow-card-rest);
  }

  .card-3d:hover {
    box-shadow: var(--shadow-card-hover);
  }

  /* 标准化卡片样式 */
  .card-base {
    @apply rounded-xl border border-border bg-card text-card-foreground;
    @apply shadow-card transition-all duration-300;
  }

  .card-elevated {
    @apply card-base shadow-lg hover:shadow-xl;
    @apply hover:-translate-y-1;
  }

  .card-interactive {
    @apply card-base hover:shadow-glow hover:scale-[1.02];
    @apply cursor-pointer;
  }

  .card-glass {
    @apply rounded-xl border border-border/50;
    backdrop-filter: var(--glass-effect);
    background: rgba(255, 255, 255, 0.08);
    @apply shadow-card hover:shadow-glow transition-all duration-300;
  }

  /* 标准化表单样式 */
  .form-input {
    @apply flex h-10 w-full rounded-lg border border-input bg-background px-3 py-2;
    @apply text-sm ring-offset-background file:border-0 file:bg-transparent;
    @apply file:text-sm file:font-medium placeholder:text-muted-foreground;
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring;
    @apply focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
    @apply transition-all duration-200;
  }

  .form-textarea {
    @apply flex min-h-[80px] w-full rounded-lg border border-input bg-background px-3 py-2;
    @apply text-sm ring-offset-background placeholder:text-muted-foreground;
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring;
    @apply focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
    @apply transition-all duration-200 resize-none;
  }

  .form-select {
    @apply flex h-10 w-full items-center justify-between rounded-lg border border-input;
    @apply bg-background px-3 py-2 text-sm ring-offset-background;
    @apply placeholder:text-muted-foreground focus:outline-none focus:ring-2;
    @apply focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
    @apply transition-all duration-200;
  }

  .form-label {
    @apply text-sm font-medium leading-none;
    @apply peer-disabled:cursor-not-allowed peer-disabled:opacity-70;
  }

  .form-error {
    @apply text-sm font-medium text-error;
  }

  .form-helper {
    @apply text-sm text-muted-foreground;
  }

  .glass-effect {
    backdrop-filter: var(--glass-effect);
    border: 1px solid var(--glass-border);
    background: rgba(255, 255, 255, 0.08);
  }

  .pulse-button {
    @apply relative overflow-hidden;
  }

  .floating-element {
    /* 移除持续浮动动画 */
  }

  .shimmer {
    background: linear-gradient(90deg,
      hsl(0 0% 100% / 0) 0%,
      hsl(0 0% 100% / 0.2) 50%,
      hsl(0 0% 100% / 0) 100%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }

  .magnetic {
    transition: transform 0.2s ease-out;
  }

  /* 文本截断工具类 */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* 状态指示器 */
  .status-success {
    @apply bg-success text-success-foreground;
  }

  .status-warning {
    @apply bg-warning text-warning-foreground;
  }

  .status-error {
    @apply bg-error text-error-foreground;
  }

  .status-info {
    @apply bg-info text-info-foreground;
  }

  .status-success-light {
    @apply bg-success-light text-success border border-success/20;
  }

  .status-warning-light {
    @apply bg-warning-light text-warning border border-warning/20;
  }

  .status-error-light {
    @apply bg-error-light text-error border border-error/20;
  }

  .status-info-light {
    @apply bg-info-light text-info border border-info/20;
  }

  /* 实用工具类 */
  .text-gradient {
    background: var(--gradient-text);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .bg-gradient-primary {
    background: var(--gradient-primary);
  }

  .bg-gradient-secondary {
    background: var(--gradient-secondary);
  }

  .bg-gradient-hero {
    background: var(--gradient-hero);
  }

  .bg-gradient-card {
    background: var(--gradient-card);
  }

  .bg-gradient-card-hover {
    background: var(--gradient-card-hover);
  }

  .shadow-card {
    box-shadow: var(--shadow-card);
  }

  .shadow-glow {
    box-shadow: var(--shadow-glow);
  }

  .shadow-glow-intense {
    box-shadow: var(--shadow-glow-intense);
  }

  .transform-gpu {
    transform: translateZ(0);
  }

  /* 响应式工具类 */
  .mobile-only {
    @apply block sm:hidden;
  }

  .tablet-only {
    @apply hidden sm:block lg:hidden;
  }

  .desktop-only {
    @apply hidden lg:block;
  }

  .mobile-tablet {
    @apply block lg:hidden;
  }

  .tablet-desktop {
    @apply hidden sm:block;
  }

  /* 响应式间距 */
  .responsive-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .responsive-margin {
    @apply mx-4 sm:mx-6 lg:mx-8;
  }

  .responsive-gap {
    @apply gap-4 sm:gap-6 lg:gap-8;
  }

  /* 响应式文字大小 */
  .text-responsive-xs {
    @apply text-xs sm:text-sm;
  }

  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }

  .text-responsive-base {
    @apply text-base sm:text-lg;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl lg:text-2xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl lg:text-3xl;
  }

  .text-responsive-2xl {
    @apply text-2xl sm:text-3xl lg:text-4xl;
  }

  .text-responsive-3xl {
    @apply text-3xl sm:text-4xl lg:text-5xl;
  }

  /* 动画工具类 */
  .animate-fade-in {
    animation: fade-in-up 0.6s ease-out;
  }

  .animate-slide-in {
    animation: slide-in-bottom 0.6s ease-out;
  }

  .animate-bounce-in {
    animation: bounce-in 0.8s ease-out;
  }

  .animate-scale-in {
    animation: scale-in 0.4s ease-out;
  }

  .animate-blur-fade-in {
    animation: blur-fade-in 0.8s ease-out;
  }

  .animate-rotate-in {
    animation: rotate-in 0.6s ease-out;
  }

  .animate-glow-pulse {
    animation: glow-pulse 2s ease-in-out infinite;
  }

  .animate-shimmer {
    animation: shimmer 1.5s ease-in-out infinite;
  }

  /* 延迟动画 */
  .animate-delay-100 {
    animation-delay: 0.1s;
  }

  .animate-delay-200 {
    animation-delay: 0.2s;
  }

  .animate-delay-300 {
    animation-delay: 0.3s;
  }

  .animate-delay-500 {
    animation-delay: 0.5s;
  }

  /* 加载动画 */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-muted border-t-primary;
  }

  .loading-dots::after {
    content: '';
    animation: loading-dots 1.5s infinite;
  }

  .loading-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* ===== 微交互系统 ===== */

  /* 按钮微交互 */
  .btn-interactive {
    @apply transition-all duration-300 ease-out transform-gpu;
    @apply hover:scale-105 hover:shadow-lg active:scale-95;
    @apply focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2;
  }

  .btn-primary-interactive {
    @apply btn-interactive;
    @apply bg-gradient-to-r from-primary to-primary-glow;
    @apply hover:from-primary-glow hover:to-primary;
    @apply hover:shadow-primary/25 active:shadow-primary/40;
    position: relative;
    overflow: hidden;
  }

  .btn-primary-interactive::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
  }

  .btn-primary-interactive:hover::before {
    left: 100%;
  }

  .btn-secondary-interactive {
    @apply btn-interactive;
    @apply border-2 border-primary/20 bg-background;
    @apply hover:border-primary hover:bg-primary/5;
    @apply hover:text-primary;
  }

  .btn-ghost-interactive {
    @apply btn-interactive;
    @apply hover:bg-primary/10 hover:text-primary;
  }

  /* 卡片微交互 */
  .card-interactive {
    @apply transition-all duration-300 ease-out transform-gpu;
    @apply hover:scale-[1.02] hover:shadow-xl;
    @apply hover:-translate-y-1;
    @apply cursor-pointer;
    position: relative;
    overflow: hidden;
  }

  .card-interactive::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, hsl(var(--primary) / 0.05) 50%, transparent 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }

  .card-interactive:hover::before {
    opacity: 1;
  }

  .card-magnetic {
    transition: transform 0.3s ease-out;
  }

  /* 链接微交互 */
  .link-interactive {
    @apply relative transition-colors duration-300;
    @apply hover:text-primary;
  }

  .link-interactive::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    transition: width 0.3s ease;
  }

  .link-interactive:hover::after {
    width: 100%;
  }

  /* 输入框微交互 */
  .input-interactive {
    @apply transition-all duration-300 ease-out;
    @apply focus:scale-[1.02] focus:shadow-lg;
    @apply focus:border-primary focus:ring-2 focus:ring-primary/20;
  }

  /* 图标微交互 */
  .icon-interactive {
    @apply transition-all duration-300 ease-out transform-gpu;
    @apply hover:scale-110 hover:rotate-12;
    @apply hover:text-primary;
  }

  .icon-bounce {
    @apply hover:animate-bounce;
  }

  .icon-spin {
    @apply hover:animate-spin;
  }

  /* 徽章微交互 */
  .badge-interactive {
    @apply transition-all duration-300 ease-out transform-gpu;
    @apply hover:scale-105 hover:shadow-md;
    @apply hover:bg-primary/15 hover:border-primary/40;
  }

  /* 涟漪效果 */
  .ripple-effect {
    position: relative;
    overflow: hidden;
  }

  .ripple-effect::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
  }

  .ripple-effect:active::before {
    width: 300px;
    height: 300px;
  }

  /* 悬停状态增强 */
  .hover-lift {
    @apply transition-all duration-300 ease-out transform-gpu;
    @apply hover:-translate-y-2 hover:shadow-xl;
  }

  .hover-glow {
    @apply transition-all duration-300 ease-out;
    @apply hover:shadow-glow hover:shadow-primary/25;
  }

  .hover-scale {
    @apply transition-transform duration-300 ease-out transform-gpu;
    @apply hover:scale-105;
  }

  .hover-rotate {
    @apply transition-transform duration-300 ease-out transform-gpu;
    @apply hover:rotate-6;
  }

  /* 焦点状态增强 */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2;
    @apply focus:ring-offset-background;
  }

  .focus-glow {
    @apply focus:outline-none focus:shadow-glow focus:shadow-primary/30;
  }

  /* 加载状态微交互 */
  .loading-skeleton {
    @apply animate-pulse bg-gradient-to-r from-muted via-muted/50 to-muted;
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s ease-in-out infinite;
  }

  .loading-dots-bounce {
    display: inline-flex;
    gap: 0.25rem;
  }

  .loading-dots-bounce span {
    width: 0.5rem;
    height: 0.5rem;
    background: hsl(var(--primary));
    border-radius: 50%;
    animation: dots-bounce 1.4s ease-in-out infinite both;
  }

  .loading-dots-bounce span:nth-child(1) { animation-delay: -0.32s; }
  .loading-dots-bounce span:nth-child(2) { animation-delay: -0.16s; }
  .loading-dots-bounce span:nth-child(3) { animation-delay: 0s; }

  /* 页面转场效果 */
  .page-transition-enter {
    opacity: 0;
    transform: translateY(20px);
  }

  .page-transition-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.3s ease-out, transform 0.3s ease-out;
  }

  .page-transition-exit {
    opacity: 1;
    transform: translateY(0);
  }

  .page-transition-exit-active {
    opacity: 0;
    transform: translateY(-20px);
    transition: opacity 0.3s ease-out, transform 0.3s ease-out;
  }

  /* 表单微交互 */
  .form-field-interactive {
    @apply transition-all duration-300 ease-out;
    @apply focus-within:scale-[1.02] focus-within:shadow-lg;
    @apply focus-within:border-primary;
  }

  .form-label-interactive {
    @apply transition-all duration-300 ease-out;
    @apply peer-focus:text-primary peer-focus:scale-105;
    transform-origin: left center;
  }

  /* 通知和提示微交互 */
  .notification-slide-in {
    animation: notification-slide-in 0.3s ease-out;
  }

  .notification-slide-out {
    animation: notification-slide-out 0.3s ease-out;
  }

  .tooltip-fade-in {
    animation: tooltip-fade-in 0.2s ease-out;
  }

  /* 导航微交互 */
  .nav-item-interactive {
    @apply relative transition-all duration-300 ease-out;
    @apply hover:text-primary;
  }

  .nav-item-interactive::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: hsl(var(--primary));
    transition: all 0.3s ease;
    transform: translateX(-50%);
  }

  .nav-item-interactive:hover::before,
  .nav-item-interactive.active::before {
    width: 100%;
  }

  /* 响应式卡片优化 */
  .hover-card {
    @apply transition-all duration-200 ease-out;
  }

  .hover-card:hover {
    @apply shadow-lg scale-[1.01];
  }

  /* 透明卡片样式 */
  .card-transparent {
    background: transparent !important;
    border: 1px solid hsl(var(--border) / 0.3) !important;
    box-shadow: none !important;
  }

  .card-minimal {
    background: hsl(var(--background)) !important;
    border: 1px solid hsl(var(--border) / 0.5) !important;
    box-shadow: none !important;
  }

  .card-subtle {
    background: hsl(var(--muted) / 0.3) !important;
    border: 1px solid hsl(var(--border) / 0.4) !important;
    box-shadow: 0 1px 3px 0 hsl(var(--foreground) / 0.05) !important;
  }

  /* 小屏幕优化 */
  @media (max-width: 640px) {
    .container {
      @apply px-3;
    }

    .card-compact {
      @apply p-3;
    }
  }

  /* 移动端布局修复 - 最大化屏幕利用率 */
  @media (max-width: 768px) {
    /* 防止水平滚动 */
    body {
      overflow-x: hidden;
    }

    /* 确保所有容器不超出视口宽度，极小化边距 */
    .max-w-7xl {
      max-width: 100vw;
      padding-left: 0.0625rem; /* 1px */
      padding-right: 0.0625rem; /* 1px */
    }

    /* 移动端卡片内边距优化 - 增加文字与边框距离 */
    .mobile-card-padding {
      @apply px-3 py-2; /* 增加左右内边距让文字离边框更远 */
    }

    /* 移动端按钮组布局 */
    .mobile-button-group {
      @apply flex flex-wrap gap-1 items-center; /* 减少间距 */
    }

    /* 移动端文字大小优化 - 保持适当行间距 */
    .mobile-text-base {
      font-size: 16px;
      line-height: 1.5; /* 保持舒适的行间距 */
    }

    .mobile-text-sm {
      font-size: 14px;
      line-height: 1.4; /* 保持舒适的行间距 */
    }

    .mobile-text-xs {
      font-size: 13px;
      line-height: 1.3; /* 保持舒适的行间距 */
    }

    /* 移动端收藏按钮优化 */
    .mobile-favorite-button {
      @apply px-1 py-1 text-xs min-w-0 flex-shrink-0; /* 减少内边距 */
      max-width: calc(50% - 0.125rem);
    }

    /* 移动端内容区域优化 - 极小化边距 */
    .mobile-content-area {
      @apply px-0; /* 无边距 */
      width: 100%;
      max-width: 100vw;
      box-sizing: border-box;
    }

    /* 移动端导航栏优化 */
    .mobile-navbar {
      @apply px-1; /* 极小导航栏边距 */
    }

    /* 移动端卡片间距优化 */
    .mobile-card-spacing {
      @apply space-y-2; /* 减少卡片间距 */
    }

    /* 移动端页面内边距优化 */
    .mobile-page-padding {
      @apply py-2; /* 减少页面上下边距 */
    }

    /* 移动端标题优化 */
    .mobile-title {
      @apply mb-2; /* 减少标题下边距 */
    }

    /* 移动端统计卡片优化 */
    .mobile-stats-card {
      @apply p-2; /* 减少统计卡片内边距 */
    }

    /* 移动端过滤器优化 */
    .mobile-filters {
      @apply p-2; /* 减少过滤器内边距 */
    }

    /* 移动端Badge间距优化 */
    .mobile-badge-spacing {
      @apply gap-1; /* 减少Badge间距 */
    }

    /* 移动端按钮间距优化 */
    .mobile-button-spacing {
      @apply gap-1; /* 减少按钮间距 */
    }

    /* 移动端表单间距优化 */
    .mobile-form-spacing {
      @apply space-y-2; /* 减少表单元素间距 */
    }

    /* 移动端网格间距优化 */
    .mobile-grid-gap {
      @apply gap-1; /* 减少网格间距 */
    }

    /* 移动端内容最大化 */
    .mobile-maximize-content {
      @apply px-1 py-1; /* 最小化所有边距 */
    }

    /* 移动端极致优化 - 95%屏幕利用率 */
    body {
      margin: 0;
      padding: 0;
    }

    /* 移动端容器极致优化 - 无边距 */
    .mobile-ultra-compact {
      margin: 0 !important;
      padding: 0 !important; /* 完全无边距 */
    }

    /* 移动端卡片极致优化 */
    .mobile-ultra-card {
      margin: 0.125rem 0 !important; /* 2px 上下边距 */
      padding: 0.5rem !important; /* 8px 内边距 - 增加文字与边框距离 */
    }

    /* 移动端文本极致优化 - 保持可读性 */
    .mobile-ultra-text {
      margin: 0 !important;
      padding: 0 !important;
      line-height: 1.4 !important; /* 增加行间距保持可读性 */
    }

    /* 移动端按钮极致优化 */
    .mobile-ultra-button {
      padding: 0.125rem 0.25rem !important; /* 2px 4px */
      margin: 0.125rem !important; /* 2px */
      min-height: auto !important;
    }

    /* 移动端Badge极致优化 */
    .mobile-ultra-badge {
      padding: 0.125rem 0.25rem !important; /* 2px 4px */
      margin: 0.125rem !important; /* 2px */
      font-size: 0.75rem !important; /* 12px */
    }

    /* 移动端Markdown内容优化 - 保持可读性，缩小间距一半 */
    .mobile-markdown-content {
      line-height: 1.7 !important; /* 保持普通文字行间距不变 */
      margin-bottom: 0.25rem !important; /* 从0.5rem缩小到0.25rem */
    }

    .mobile-markdown-content p {
      margin-bottom: 0.25rem !important; /* 从0.5rem缩小到0.25rem */
      line-height: 1.7 !important; /* 保持普通文字行间距不变 */
    }

    .mobile-markdown-content ul,
    .mobile-markdown-content ol {
      margin-bottom: 0.25rem !important; /* 从0.5rem缩小到0.25rem */
      margin-top: 0.125rem !important; /* 从0.25rem缩小到0.125rem */
      padding-left: 1rem !important;
    }

    .mobile-markdown-content li {
      line-height: 1.6 !important; /* 保持列表项行间距不变 */
      margin-bottom: 0.125rem !important; /* 从0.25rem缩小到0.125rem */
    }

    /* 移动端标题和内容区域极致优化 */
    .mobile-title-ultra {
      margin-left: 0.25rem !important; /* 4px 左边距 */
      margin-right: 0.25rem !important; /* 4px 右边距 */
    }

    .mobile-content-ultra {
      margin-left: 0.125rem !important; /* 2px 左边距 */
      margin-right: 0.125rem !important; /* 2px 右边距 */
      padding-left: 0.5rem !important; /* 8px 左内边距 - 增加文字离边框距离 */
      padding-right: 0.5rem !important; /* 8px 右内边距 - 增加文字离边框距离 */
    }

    /* 移动端边框扩展 - 让边框线更贴近屏幕边缘但保持可见 */
    .mobile-border-expand {
      margin-left: -0.25rem !important; /* 向左扩展4px */
      margin-right: -0.25rem !important; /* 向右扩展4px */
      border-radius: 0.25rem !important; /* 保持小圆角 */
    }

    /* 移动端卡片边框扩展 */
    .mobile-card-expand {
      margin-left: -0.125rem !important; /* 向左扩展2px */
      margin-right: -0.125rem !important; /* 向右扩展2px */
      border-radius: 0.25rem !important; /* 保持小圆角 */
    }

    /* 移动端文字内边距优化 */
    .mobile-text-padding {
      padding-left: 0.75rem !important; /* 12px 左内边距 */
      padding-right: 0.75rem !important; /* 12px 右内边距 */
    }
  }

  .text-gradient {
    @apply bg-gradient-primary bg-clip-text text-transparent;
  }

  .blur-fade-in {
    animation: blur-fade-in 0.6s ease-out forwards;
  }

  .bounce-in {
    animation: bounce-in 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
  }

  .slide-in-bottom {
    animation: slide-in-bottom 0.6s ease-out forwards;
  }

  .rotate-in {
    animation: rotate-in 0.5s ease-out forwards;
  }

  .fade-in-up {
    animation: fade-in-up 0.6s ease-out forwards;
  }

  .scale-in {
    animation: scale-in 0.4s ease-out forwards;
  }

  .glow-pulse {
    animation: glow-pulse 2s ease-in-out infinite;
  }

  /* 文本截断工具类 */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 现代化按钮效果 */
  .modern-button {
    @apply relative overflow-hidden rounded-xl px-6 py-3;
    @apply bg-gradient-primary text-primary-foreground;
    @apply transition-all duration-300 ease-out;
    @apply hover:shadow-glow hover:scale-105;
    @apply active:scale-95;
  }

  .modern-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }

  .modern-button:hover::before {
    left: 100%;
  }

  /* 卡片悬浮效果 */
  .hover-card {
    @apply transition-all duration-300 ease-out;
    @apply hover:shadow-glow hover:-translate-y-1;
    @apply hover:bg-gradient-card;
  }

  /* 渐变边框 */
  .gradient-border {
    background: linear-gradient(hsl(var(--background)), hsl(var(--background))) padding-box,
                var(--gradient-primary) border-box;
    border: 2px solid transparent;
  }

  /* Markdown 内容样式 */
  .markdown-content {
    @apply text-sm leading-relaxed;
    line-height: 1.8 !important; /* 保持普通文字行间距不变 */
  }

  /* Markdown 段落间距 - 缩小一半 */
  .markdown-content p {
    margin-bottom: 0.3rem !important; /* 从0.6rem缩小到0.3rem */
    line-height: 1.8 !important; /* 保持普通文字行间距不变 */
  }

  /* Markdown 列表间距 - 缩小一半 */
  .markdown-content ul,
  .markdown-content ol {
    margin-bottom: 0.3rem !important; /* 从0.6rem缩小到0.3rem */
    margin-top: 0.15rem !important; /* 从0.3rem缩小到0.15rem */
  }

  /* 所有摘要页面字体放大1.1倍 */
  .content-summary-enlarged {
    font-size: 1.1em;
  }

  .content-summary-enlarged .mobile-text-base {
    font-size: calc(16px * 1.1);
  }

  .content-summary-enlarged .mobile-text-sm {
    font-size: calc(14px * 1.1);
  }

  .content-summary-enlarged .mobile-text-xs {
    font-size: calc(13px * 1.1);
  }

  .content-summary-enlarged .text-xs {
    font-size: calc(0.75rem * 1.1);
  }

  .content-summary-enlarged .text-sm {
    font-size: calc(0.875rem * 1.1);
  }

  .content-summary-enlarged .text-base {
    font-size: calc(1rem * 1.1);
  }

  .content-summary-enlarged .text-lg {
    font-size: calc(1.125rem * 1.1);
  }

  .content-summary-enlarged .text-xl {
    font-size: calc(1.25rem * 1.1);
  }

  .content-summary-enlarged .text-2xl {
    font-size: calc(1.5rem * 1.1);
  }

  .content-summary-enlarged .text-3xl {
    font-size: calc(1.875rem * 1.1);
  }

  .content-summary-enlarged .text-4xl {
    font-size: calc(2.25rem * 1.1);
  }

  .content-summary-enlarged .mobile-ultra-badge {
    font-size: calc(0.75rem * 1.1) !important;
  }

  /* 标题间距 - 缩小一半 */
  .markdown-content h1 {
    @apply text-2xl font-bold;
    margin-top: 1rem !important; /* 从mt-8(2rem)缩小到1rem */
    margin-bottom: 0.5rem !important; /* 从mb-4(1rem)缩小到0.5rem */
  }

  .markdown-content h2 {
    @apply text-xl font-bold;
    margin-top: 0.75rem !important; /* 从mt-6(1.5rem)缩小到0.75rem */
    margin-bottom: 0.375rem !important; /* 从mb-3(0.75rem)缩小到0.375rem */
  }

  .markdown-content h3 {
    @apply text-lg font-semibold;
    margin-top: 0.5rem !important; /* 从mt-4(1rem)缩小到0.5rem */
    margin-bottom: 0.25rem !important; /* 从mb-2(0.5rem)缩小到0.25rem */
  }

  /* 列表项间距 - 缩小一半 */
  .markdown-content li {
    margin-bottom: 0.125rem !important; /* 从mb-1(0.25rem)缩小到0.125rem */
    line-height: 1.7 !important; /* 保持列表项行间距不变 */
  }

  /* 确保列表项的缩进样式生效，覆盖任何默认样式 */
  .markdown-content li.ml-6 {
    margin-left: 1.5rem !important;
  }

  .markdown-content li.ml-12 {
    margin-left: 3rem !important;
  }

  .markdown-content li.ml-18 {
    margin-left: 4.5rem !important;
  }

  .markdown-content li.ml-24 {
    margin-left: 6rem !important;
  }

  .markdown-content strong {
    @apply font-semibold;
  }

  .markdown-content em {
    @apply italic;
  }

  .markdown-content a {
    @apply text-blue-600 hover:text-blue-800 underline;
  }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 5px hsl(15 90% 55% / 0.3); }
  50% { box-shadow: 0 0 20px hsl(15 90% 55% / 0.6), 0 0 30px hsl(315 85% 60% / 0.4); }
}

@keyframes pulse-ring {
  0% { width: 0; height: 0; opacity: 1; }
  100% { width: 200px; height: 200px; opacity: 0; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes blur-fade-in {
  0% {
    opacity: 0;
    filter: blur(10px);
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    filter: blur(0);
    transform: scale(1);
  }
}

@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3) translateY(50px);
  }
  50% {
    opacity: 1;
    transform: scale(1.05) translateY(-10px);
  }
  70% {
    transform: scale(0.95) translateY(0);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes slide-in-bottom {
  0% {
    opacity: 0;
    transform: translateY(100px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes rotate-in {
  0% {
    opacity: 0;
    transform: rotate(-10deg) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: rotate(0deg) scale(1);
  }
}

@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scale-in {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 20px hsl(15 90% 55% / 0.3);
  }
  50% {
    box-shadow: 0 0 40px hsl(15 90% 55% / 0.6), 0 0 60px hsl(315 85% 60% / 0.4);
  }
}

@keyframes loading-dots {
  0%, 20% {
    content: '.';
  }
  40% {
    content: '..';
  }
  60%, 100% {
    content: '...';
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes slide-in-left {
  0% {
    opacity: 0;
    transform: translateX(-100px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  0% {
    opacity: 0;
    transform: translateX(100px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes zoom-in {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes flip-in {
  0% {
    opacity: 0;
    transform: rotateY(-90deg);
  }
  100% {
    opacity: 1;
    transform: rotateY(0);
  }
}

/* ===== 微交互关键帧动画 ===== */

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes dots-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes notification-slide-in {
  0% {
    opacity: 0;
    transform: translateX(100%);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes notification-slide-out {
  0% {
    opacity: 1;
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(100%);
  }
}

@keyframes tooltip-fade-in {
  0% {
    opacity: 0;
    transform: translateY(10px) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes button-press {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes card-hover-lift {
  0% {
    transform: translateY(0) scale(1);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
  100% {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
}

@keyframes icon-bounce-in {
  0% {
    transform: scale(0.3) rotate(-120deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.05) rotate(-10deg);
  }
  70% {
    transform: scale(0.9) rotate(5deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

@keyframes text-glow {
  0%, 100% {
    text-shadow: 0 0 5px hsl(var(--primary) / 0.3);
  }
  50% {
    text-shadow: 0 0 20px hsl(var(--primary) / 0.6), 0 0 30px hsl(var(--primary-glow) / 0.4);
  }
}

@keyframes border-glow {
  0%, 100% {
    border-color: hsl(var(--border));
    box-shadow: 0 0 0 0 hsl(var(--primary) / 0);
  }
  50% {
    border-color: hsl(var(--primary));
    box-shadow: 0 0 0 3px hsl(var(--primary) / 0.2);
  }
}

@keyframes progress-bar {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

@keyframes elastic-scale {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  75% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}